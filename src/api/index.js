/* eslint-disable no-unused-vars */
import request from "@/utils/request";
import config, { API_ENDPOINTS } from "@/config/api";
import mockData from "../mock/data";

// 在实际项目中，这里会配置真实的API请求
// 现在使用真实的后端API

export const getDashboardData = async (storeId = "1001", date = null, timeRange = "today") => {
  try {
    const response = await request.post(`${config.apiPrefix}${API_ENDPOINTS.DASHBOARD_DATA}`, {
      storeId: String(storeId),
      date: date,
      timeRange: timeRange
    });

    // 新的响应格式：rsCode='00000000'表示成功，数据在body字段中
    if (response.rsCode === '00000000') {
      console.log("Dashboard API调用成功:", response.body);
      return response.body;
    } else {
      console.error("获取Dashboard数据失败:", response.msg);
      // 如果API失败，返回模拟数据作为备用
      return mockData.dashboardData;
    }
  } catch (error) {
    console.error("Dashboard API请求失败:", error);
    // 如果请求失败，返回模拟数据作为备用
    return mockData.dashboardData;
  }
};

export const getPosUsageData = async (params) => {
  try {
    const { storeId = "1001", date = null } = params || {};
    const response = await request.post(`${config.apiPrefix}${API_ENDPOINTS.POS_USAGE}`, {
      storeId: String(storeId),
      date: date,
      timeRange: "today"
    });

    // 新的响应格式：rsCode='00000000'表示成功，数据在body字段中
    if (response.rsCode === '00000000') {
      console.log("POS Usage API调用成功:", response.body);
      return [response.body]; // 包装成数组格式以匹配前端期望
    } else {
      console.error("获取POS使用率数据失败:", response.msg);
      // 如果API失败，返回模拟数据作为备用
      let data = mockData.posUsageData;
      if (storeId) {
        data = data.filter((item) => item.storeId === storeId);
      }
      return data;
    }
  } catch (error) {
    console.error("POS Usage API请求失败:", error);
    // 如果请求失败，返回模拟数据作为备用
    const { storeId } = params || {};
    let data = mockData.posUsageData;
    if (storeId) {
      data = data.filter((item) => item.storeId === storeId);
    }
    return data;
  }
};

export const getOrderAnalysisData = async (params) => {
  try {
    const { storeId = "1001", date = null } = params || {};
    const response = await request.post(`${config.apiPrefix}${API_ENDPOINTS.ORDER_ANALYSIS}`, {
      storeId: String(storeId),
      date: date,
      timeRange: "today"
    });

    // 新的响应格式：rsCode='00000000'表示成功，数据在body字段中
    if (response.rsCode === '00000000') {
      console.log("Order Analysis API调用成功:", response.body);
      return response.body;
    } else {
      console.error("获取订单分析数据失败:", response.msg);
      // 如果API失败，返回模拟数据作为备用
      return mockData.orderAnalysisData;
    }
  } catch (error) {
    console.error("Order Analysis API请求失败:", error);
    // 如果请求失败，返回模拟数据作为备用
    return mockData.orderAnalysisData;
  }
};

export const getDeviceStatusData = async (params) => {
  try {
    const { storeId = "1001", date = null } = params || {};
    const response = await request.post(`${config.apiPrefix}${API_ENDPOINTS.DEVICE_STATUS}`, {
      storeId: String(storeId),
      date: date,
      timeRange: "today"
    });

    // 新的响应格式：rsCode='00000000'表示成功，数据在body字段中
    if (response.rsCode === '00000000') {
      console.log("Device Status API调用成功:", response.body);
      return response.body;
    } else {
      console.error("获取设备状态数据失败:", response.msg);
      // 如果API失败，返回模拟数据作为备用
      return mockData.deviceStatusData;
    }
  } catch (error) {
    console.error("Device Status API请求失败:", error);
    // 如果请求失败，返回模拟数据作为备用
    return mockData.deviceStatusData;
  }
};

export const getEnergyOptimizationData = async (params) => {
  try {
    const { storeId = "1001", date = null } = params || {};
    const response = await request.post(`${config.apiPrefix}${API_ENDPOINTS.ENERGY_OPTIMIZATION}`, {
      storeId: String(storeId),
      date: date,
      timeRange: "day"
    });

    // 新的响应格式：rsCode='00000000'表示成功，数据在body字段中
    if (response.rsCode === '00000000') {
      console.log("Energy Optimization API调用成功:", response.body);
      return response.body;
    } else {
      console.error("获取能耗优化数据失败:", response.msg);
      // 如果API失败，返回模拟数据作为备用
      return mockData.energyOptimizationData;
    }
  } catch (error) {
    console.error("Energy Optimization API请求失败:", error);
    // 如果请求失败，返回模拟数据作为备用
    return mockData.energyOptimizationData;
  }
};

export const getStoreList = async () => {
  try {
    const response = await request.post(`${config.apiPrefix}${API_ENDPOINTS.STORE_LIST}`, {});

    // 新的响应格式：rsCode='00000000'表示成功，数据在body字段中
    if (response.rsCode === '00000000') {
      console.log("Store List API调用成功:", response.body);
      return response.body.stores || [];
    } else {
      console.error("获取门店列表失败:", response.msg);
      // 如果API失败，返回模拟数据作为备用
      return mockData.storeList;
    }
  } catch (error) {
    console.error("Store List API请求失败:", error);
    // 如果请求失败，返回模拟数据作为备用
    return mockData.storeList;
  }
};

export const getEnergyConsumption = async (params) => {
  try {
    const { storeId = "1001", date = null, timeRange = "daily", chartType = "consumption" } = params || {};
    const response = await request.post(`${config.apiPrefix}${API_ENDPOINTS.ENERGY_CONSUMPTION}`, {
      storeId: String(storeId),
      date: date,
      timeRange: timeRange,
      chartType: chartType
    });

    // 新的响应格式：rsCode='00000000'表示成功，数据在body字段中
    if (response.rsCode === '00000000') {
      console.log("Energy Consumption API调用成功:", response.body);
      return { data: response.body.deviceDetails || [] }; // 包装成前端期望的格式
    } else {
      console.error("获取能耗数据失败:", response.msg);
      // 如果API失败，返回模拟数据作为备用
      return Promise.resolve(mockData.energyConsumptionData);
    }
  } catch (error) {
    console.error("Energy Consumption API请求失败:", error);
    // 如果请求失败，返回模拟数据作为备用
    return Promise.resolve(mockData.energyConsumptionData);
  }
};

// -------------------获取门店POS状态的API接口----------------------
export const getPosStatus = async (storeNo = 1001) => {
  try {
    const response = await request.post(`${config.apiPrefix}${API_ENDPOINTS.POS_STATUS}`, {
      storeNo: Number(storeNo),
    });
    return response.body;
  } catch (error) {
    console.error("获取门店POS状态失败:", error);
    throw error;
  }
};



// ==================== 登录相关API ====================

// 发送验证码
export const sendVerificationCode = async (phone) => {
  try {
    const response = await request.post(`${config.apiPrefix}/api/auth/send-code`, {
      mobile: phone
    });
    return response;
  } catch (error) {
    console.error("发送验证码失败:", error);
    throw error;
  }
};

// 手机号验证码登录
export const loginWithCode = async (phone, code) => {
  try {
    const response = await request.post(`${config.apiPrefix}/api/auth/login`, {
      mobile: phone,
      code,
      type: 1
    });
    return response;
  } catch (error) {
    console.error("登录失败:", error);
    throw error;
  }
};

// 获取用户信息
export const getUserInfo = async () => {
  try {
    const response = await request.post(`${config.apiPrefix}/api/auth/query-emp-dept-role`);
    return response;
  } catch (error) {
    console.error("获取用户信息失败:", error);
    throw error;
  }
};

// 登出
export const logout = async () => {
  try {
    const response = await request.post(`${config.apiPrefix}/auth/logout`);
    return response;
  } catch (error) {
    console.error("登出失败:", error);
    throw error;
  }
};

// -------------------获取门店数据的API接口----------------------
export const getPosStore = async () => {
  try {
    console.log("开始请求门店数据...");
    console.log("API配置:", config);
    console.log("请求URL:", `${config.apiPrefix}${API_ENDPOINTS.POS_STORE}`);

    // 使用统一的request实例发送请求
    const response = await request.post(
      `${config.apiPrefix}${API_ENDPOINTS.POS_STORE}`,
      {}
    );

    console.log("门店数据获取成功:", response.body.storeInfoList);
    return response.body.storeInfoList;
  } catch (error) {
    console.error("获取门店数据失败:", error);
    console.log("使用备用数据");

    // 返回模拟数据作为备用
    return [
      {
        storeId: "5094",
        storeName: " 长安霄边店",
        pgSeq: "5",
        pgName: "华南地区",
        subId: "5_11",
        subArea: "粤东区",
      },
      {
        storeId: "5096",
        storeName: "海源店",
        pgSeq: "5",
        pgName: "华南地区",
        subId: "5_15",
        subArea: "云贵区",
      },
      {
        storeId: "1001",
        storeName: "上海南京路店",
        pgSeq: "1",
        pgName: "华东地区",
        subId: "1_01",
        subArea: "上海区",
      },
      {
        storeId: "1002",
        storeName: "杭州西湖店",
        pgSeq: "1",
        pgName: "华东地区",
        subId: "1_02",
        subArea: "浙江区",
      },
    ];
  }
};
