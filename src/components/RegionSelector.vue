<template>
  <el-cascader
    v-model="selectedValue"
    :options="options"
    :props="cascaderProps"
    placeholder="请选择区域/城市/门店或输入门店号搜索"
    clearable
    filterable
    :filter-method="filterMethod"
    @change="handleChange"
    :loading="loading"
  />
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import { getPosStore } from "@/api";

// 接受父组件传递的初始门店ID
const props = defineProps({
  initialStoreId: {
    type: String,
    default: "1001",
  },
});

const selectedValue = ref([]);
const options = ref([]);
const loading = ref(false);

const cascaderProps = {
  expandTrigger: "hover",
  checkStrictly: false,
};

// 自定义过滤方法，支持门店号搜索
const filterMethod = (node, keyword) => {
  // 如果没有关键词，显示所有选项
  if (!keyword) return true;

  // 转换为小写进行不区分大小写的搜索
  const searchKeyword = keyword.toLowerCase();

  // 获取节点的标签文本
  const nodeLabel = node.label.toLowerCase();

  // 如果是门店节点（第三级），检查门店号和门店名称
  if (node.level === 2) {
    // 门店标签格式为 "门店号-门店名称"
    const storeInfo = node.data.storeInfo;
    if (storeInfo) {
      const storeId = String(storeInfo.storeId).toLowerCase();
      const storeName = storeInfo.storeName.toLowerCase();

      // 支持按门店号或门店名称搜索
      return (
        storeId.includes(searchKeyword) ||
        storeName.includes(searchKeyword) ||
        nodeLabel.includes(searchKeyword)
      );
    }
  }

  // 对于区域和子区域节点，检查标签是否匹配
  return nodeLabel.includes(searchKeyword);
};

const emit = defineEmits(["store-selected"]);

// 数据分组处理函数
const processStoreData = (storeList) => {
  // 首先按pgSeq分组
  const regionGroups = {};

  storeList.forEach((store) => {
    const { pgSeq, pgName, subId, subArea, storeId, storeName } = store;

    // 创建区域分组
    if (!regionGroups[pgSeq]) {
      regionGroups[pgSeq] = {
        value: pgSeq,
        label: pgName,
        children: {},
      };
    }

    // 在区域内按subId分组
    if (!regionGroups[pgSeq].children[subId]) {
      regionGroups[pgSeq].children[subId] = {
        value: subId,
        label: subArea,
        children: [],
      };
    }

    // 添加门店信息，格式为 "门店号-门店名称"
    regionGroups[pgSeq].children[subId].children.push({
      value: storeId,
      label: `${storeId}-${storeName.trim()}`,
      storeInfo: {
        storeId,
        storeName: storeName.trim(),
        pgSeq,
        pgName,
        subId,
        subArea,
      },
    });
  });

  // 转换为级联选择器需要的格式
  const result = Object.values(regionGroups).map((region) => ({
    ...region,
    children: Object.values(region.children),
  }));

  return result;
};

// 获取门店数据
const fetchStoreData = async () => {
  loading.value = true;
  try {
    const storeList = await getPosStore();
    options.value = processStoreData(storeList);

    // 数据加载完成后，恢复选择状态
    if (props.initialStoreId) {
      setSelectedByStoreId(props.initialStoreId);
    }
  } catch (error) {
    console.error("获取门店数据失败:", error);
    // 可以在这里添加错误提示
  } finally {
    loading.value = false;
  }
};

const handleChange = (value) => {
  if (value && value.length === 3) {
    // 查找选中的门店信息
    const selectedStore = findStoreInfo(value);

    emit("store-selected", {
      pgSeq: value[0],
      subId: value[1],
      storeId: value[2],
      storeInfo: selectedStore,
    });
  }
};

// 根据选择的值查找完整的门店信息
const findStoreInfo = (selectedValues) => {
  const [pgSeq, subId, storeId] = selectedValues;

  for (const region of options.value) {
    if (region.value === pgSeq) {
      for (const subArea of region.children) {
        if (subArea.value === subId) {
          for (const store of subArea.children) {
            if (store.value === storeId) {
              return store.storeInfo;
            }
          }
        }
      }
    }
  }
  return null;
};

// 根据门店ID查找并设置选择状态
const setSelectedByStoreId = (storeId) => {
  if (!storeId || !options.value.length) return;

  for (const region of options.value) {
    for (const subArea of region.children) {
      for (const store of subArea.children) {
        if (store.value === storeId) {
          selectedValue.value = [region.value, subArea.value, store.value];
          console.log(`恢复门店选择: ${store.label}`);
          return;
        }
      }
    }
  }
  console.log(`未找到门店ID: ${storeId}`);
};

// 监听初始门店ID的变化
watch(
  () => props.initialStoreId,
  (newStoreId) => {
    if (newStoreId && options.value.length) {
      setSelectedByStoreId(newStoreId);
    }
  }
);

// 组件挂载时获取数据
onMounted(() => {
  fetchStoreData();
});
</script>

<style scoped>
.el-cascader {
  width: 300px;
}
</style>
