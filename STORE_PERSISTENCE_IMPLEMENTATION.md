# 门店选择持久化功能实现总结

## 功能概述

我已经成功为 Dashboard 组件实现了门店选择的持久化功能，现在用户选择门店后，即使刷新页面，选择的门店也会被保持。

## 实现的功能

1. **门店选择持久化** - 使用 localStorage 保存选择的门店信息
2. **页面刷新恢复** - 页面刷新后自动恢复之前选择的门店
3. **组件状态同步** - RegionSelector 组件能够根据保存的门店ID恢复选择状态
4. **数据自动加载** - 页面初始化时使用恢复的门店ID加载对应数据

## 实现的修改

### 1. Dashboard 组件修改 (`src/views/Dashboard.vue`)

#### 1.1 模板更新
```vue
<RegionSelector 
  @store-selected="handleStoreSelected" 
  :initial-store-id="currentStoreId"
/>
```
- 添加 `:initial-store-id` 属性，将当前门店ID传递给 RegionSelector 组件

#### 1.2 状态管理更新
```javascript
// 从 localStorage 恢复选择的门店ID，默认为 "1001"
const currentStoreId = ref(localStorage.getItem("selectedStoreId") || "1001");
```
- 修改 `currentStoreId` 初始化逻辑，从 localStorage 读取保存的门店ID

#### 1.3 门店选择处理更新
```javascript
const handleStoreSelected = async (selection) => {
  // ... 原有逻辑 ...
  
  // 更新当前选中的门店ID
  currentStoreId.value = storeInfo.storeId;
  
  // 保存选择的门店信息到 localStorage
  localStorage.setItem("selectedStoreId", storeInfo.storeId);
  localStorage.setItem("selectedStoreInfo", JSON.stringify(storeInfo));
  
  // ... 原有逻辑 ...
};
```
- 在门店选择时保存门店ID和完整门店信息到 localStorage

#### 1.4 初始化逻辑更新
```javascript
onMounted(async () => {
  try {
    // 使用恢复的门店ID和当前日期获取Dashboard数据
    console.log(`使用门店ID: ${currentStoreId.value} 初始化Dashboard数据`);
    const data = await getDashboardData(currentStoreId.value, currentDate.value, "today");
    dashboardData.value = data;
    console.log("初始Dashboard数据加载成功:", data);
  } catch (error) {
    console.error("获取仪表盘数据失败:", error);
  }
});
```
- 使用恢复的门店ID初始化Dashboard数据

### 2. RegionSelector 组件修改 (`src/components/RegionSelector.vue`)

#### 2.1 Props 定义
```javascript
// 接受父组件传递的初始门店ID
const props = defineProps({
  initialStoreId: {
    type: String,
    default: "1001"
  }
});
```
- 添加 `initialStoreId` prop，接受父组件传递的门店ID

#### 2.2 选择状态恢复方法
```javascript
// 根据门店ID查找并设置选择状态
const setSelectedByStoreId = (storeId) => {
  if (!storeId || !options.value.length) return;

  for (const region of options.value) {
    for (const subArea of region.children) {
      for (const store of subArea.children) {
        if (store.value === storeId) {
          selectedValue.value = [region.value, subArea.value, store.value];
          console.log(`恢复门店选择: ${store.label}`);
          return;
        }
      }
    }
  }
  console.log(`未找到门店ID: ${storeId}`);
};
```
- 新增方法，根据门店ID在选项中查找并设置选择状态

#### 2.3 数据加载完成后恢复选择
```javascript
const fetchStoreData = async () => {
  loading.value = true;
  try {
    const storeList = await getPosStore();
    options.value = processStoreData(storeList);
    
    // 数据加载完成后，恢复选择状态
    if (props.initialStoreId) {
      setSelectedByStoreId(props.initialStoreId);
    }
  } catch (error) {
    console.error("获取门店数据失败:", error);
  } finally {
    loading.value = false;
  }
};
```
- 在门店数据加载完成后自动恢复选择状态

#### 2.4 监听 Props 变化
```javascript
// 监听初始门店ID的变化
watch(() => props.initialStoreId, (newStoreId) => {
  if (newStoreId && options.value.length) {
    setSelectedByStoreId(newStoreId);
  }
});
```
- 监听 `initialStoreId` 的变化，动态更新选择状态

## 功能特性

### 1. 持久化存储
- ✅ **门店ID存储**: `localStorage.setItem("selectedStoreId", storeId)`
- ✅ **门店信息存储**: `localStorage.setItem("selectedStoreInfo", JSON.stringify(storeInfo))`
- ✅ **自动恢复**: 页面加载时从 localStorage 读取保存的门店ID

### 2. 状态同步
- ✅ **组件通信**: Dashboard 通过 props 将门店ID传递给 RegionSelector
- ✅ **选择恢复**: RegionSelector 根据传入的门店ID恢复选择状态
- ✅ **数据一致性**: 确保UI显示和实际数据加载使用相同的门店ID

### 3. 用户体验
- ✅ **无缝体验**: 刷新页面后门店选择保持不变
- ✅ **数据连续性**: 使用保存的门店ID加载对应的Dashboard数据
- ✅ **状态可见**: 用户可以清楚看到当前选择的门店

## 数据流程

```
1. 用户选择门店
   ↓
2. handleStoreSelected() 被触发
   ↓
3. 更新 currentStoreId.value
   ↓
4. 保存到 localStorage
   ↓
5. 刷新Dashboard数据
   ↓
6. 页面刷新时
   ↓
7. 从 localStorage 恢复门店ID
   ↓
8. 传递给 RegionSelector 组件
   ↓
9. RegionSelector 恢复选择状态
   ↓
10. 使用恢复的门店ID加载数据
```

## 存储的数据

### localStorage 中保存的数据：
1. **selectedStoreId**: 门店ID (字符串)
2. **selectedStoreInfo**: 完整门店信息 (JSON字符串)

### 门店信息结构：
```javascript
{
  storeId: "5094",
  storeName: "长安霄边店",
  pgSeq: "5",
  pgName: "华南地区",
  subId: "5_11",
  subArea: "粤东区"
}
```

## 测试建议

1. **基本功能测试**：
   - 选择不同门店，观察是否正确保存
   - 刷新页面，检查门店选择是否恢复
   - 检查Dashboard数据是否对应选择的门店

2. **边界情况测试**：
   - 清除 localStorage，检查是否使用默认门店
   - 选择门店后关闭浏览器重新打开
   - 检查无效门店ID的处理

3. **控制台日志**：
   - 查看门店选择和恢复的日志输出
   - 确认API调用使用正确的门店ID

## 注意事项

1. **浏览器兼容性**: localStorage 在所有现代浏览器中都支持
2. **数据清理**: 用户清除浏览器数据时会丢失保存的门店选择
3. **默认值**: 如果没有保存的门店ID，默认使用 "1001"
4. **错误处理**: 如果保存的门店ID在门店列表中不存在，会在控制台输出警告

## 部署和运行

前端应用运行在: http://localhost:8082/

现在用户可以：
1. 选择任意门店
2. 刷新页面
3. 观察到门店选择被保持
4. Dashboard数据对应选择的门店
